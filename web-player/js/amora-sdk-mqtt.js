/**
 * Amora SDK MQTT Implementation
 * 
 * Real MQTT-based implementation of the Amora SDK for web browsers.
 * Uses MQTT.js library for WebSocket MQTT communication.
 */

// Create a global namespace for the SDK
window.AmoraSDK = (function() {
  // Enums
  const PlayerState = {
    PLAYING: 'playing',
    PAUSED: 'paused',
    STOPPED: 'stopped',
    LOADING: 'loading',
    ERROR: 'error'
  };

  const ConnectionStatus = {
    CONNECTED: 'connected',
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    ERROR: 'error'
  };

  const EventType = {
    STATE_CHANGE: 'stateChange',
    POSITION_CHANGE: 'positionChange',
    VOLUME_CHANGE: 'volumeChange',
    PLAYLIST_CHANGE: 'playlistChange',
    CONNECTION_CHANGE: 'connectionChange',
    COMMAND_RESPONSE: 'commandResponse',
    ERROR: 'error'
  };

  /**
   * AmoraClient class - MQTT implementation
   */
  class AmoraClient {
    constructor(config) {
      this.config = config;
      this.eventListeners = {};
      this.connectionStatus = ConnectionStatus.DISCONNECTED;
      this.playerStatus = {
        state: PlayerState.STOPPED,
        volume: 50,
        repeat: false,
        random: false,
        position: 0,
        currentSong: null,
        playlist: null
      };
      this.playlists = [];
      this.mqttClient = null;
      this.commandId = 0;
      this.pendingCommands = new Map();
      
      // MQTT topics
      this.topics = {
        commands: `amora/devices/${config.deviceId}/commands`,
        responses: `amora/devices/${config.deviceId}/responses`,
        state: `amora/devices/${config.deviceId}/state`,
        connection: `amora/devices/${config.deviceId}/connection`
      };
    }

    // Event handling
    on(event, listener) {
      if (!this.eventListeners[event]) {
        this.eventListeners[event] = [];
      }
      this.eventListeners[event].push(listener);
      return this;
    }

    off(event, listener) {
      if (this.eventListeners[event]) {
        this.eventListeners[event] = this.eventListeners[event].filter(l => l !== listener);
      }
      return this;
    }

    emit(event, data) {
      if (this.eventListeners[event]) {
        this.eventListeners[event].forEach(listener => {
          try {
            listener(data);
          } catch (error) {
            console.error('Error in event listener:', error);
          }
        });
      }
    }

    // Generate unique command ID
    generateCommandId() {
      return `web-${Date.now()}-${++this.commandId}`;
    }

    // Send MQTT command
    sendCommand(command, params = null, timeout = 5000) {
      return new Promise((resolve, reject) => {
        if (!this.mqttClient || this.connectionStatus !== ConnectionStatus.CONNECTED) {
          reject(new Error('Not connected to MQTT broker'));
          return;
        }

        const commandId = this.generateCommandId();
        const message = {
          command,
          command_id: commandId,
          timestamp: Math.floor(Date.now() / 1000)
        };

        if (params) {
          message.params = params;
        }

        // Store pending command
        const timeoutId = setTimeout(() => {
          this.pendingCommands.delete(commandId);
          reject(new Error(`Command ${command} timed out`));
        }, timeout);

        this.pendingCommands.set(commandId, { resolve, reject, timeoutId });

        // Publish command
        try {
          this.mqttClient.publish(this.topics.commands, JSON.stringify(message));
        } catch (error) {
          this.pendingCommands.delete(commandId);
          clearTimeout(timeoutId);
          reject(error);
        }
      });
    }

    // Handle MQTT message
    handleMessage(topic, message) {
      try {
        const data = JSON.parse(message.toString());
        
        if (topic === this.topics.responses) {
          // Handle command response
          const commandId = data.command_id;
          if (this.pendingCommands.has(commandId)) {
            const { resolve, reject, timeoutId } = this.pendingCommands.get(commandId);
            clearTimeout(timeoutId);
            this.pendingCommands.delete(commandId);
            
            if (data.result) {
              resolve(data);
            } else {
              reject(new Error(data.message || 'Command failed'));
            }
          }
        } else if (topic === this.topics.state) {
          // Handle state update
          this.updatePlayerStatus(data);
        } else if (topic === this.topics.connection) {
          // Handle connection status
          console.log('Device connection status:', data);
        }
      } catch (error) {
        console.error('Error parsing MQTT message:', error);
      }
    }

    // Update player status from MQTT state message
    updatePlayerStatus(stateData) {
      const oldState = this.playerStatus.state;
      const oldVolume = this.playerStatus.volume;
      const oldPosition = this.playerStatus.position;
      const oldPlaylist = this.playerStatus.playlist;
      
      // Map MPD state to our PlayerState enum
      if (stateData.state === 'play') {
        this.playerStatus.state = PlayerState.PLAYING;
      } else if (stateData.state === 'pause') {
        this.playerStatus.state = PlayerState.PAUSED;
      } else {
        this.playerStatus.state = PlayerState.STOPPED;
      }
      
      // Update other properties
      if (stateData.volume !== undefined) {
        this.playerStatus.volume = stateData.volume;
      }
      
      if (stateData.repeat !== undefined) {
        this.playerStatus.repeat = stateData.repeat;
      }
      
      if (stateData.random !== undefined) {
        this.playerStatus.random = stateData.random;
      }

      // Update playlist
      if (stateData.playlist !== undefined) {
        this.playerStatus.playlist = stateData.playlist;
      }

      // Position is now handled in the current song update logic below
      
      // Update current song (handle partial updates)
      if (stateData.current_song) {
        // Check if this is a partial update (only position) or full song data
        const hasMetadata = stateData.current_song.title || stateData.current_song.artist || stateData.current_song.file;
        const hasPosition = stateData.current_song.position !== undefined;
        const isPartialUpdate = hasPosition && !hasMetadata;

        if (isPartialUpdate && this.playerStatus.currentSong) {
          // Only update position, preserve existing metadata
          this.playerStatus.currentSong.position = stateData.current_song.position;
          this.playerStatus.position = Math.floor(stateData.current_song.position);
        } else if (hasMetadata) {
          // Full update - replace all song data
          this.playerStatus.currentSong = {
            title: stateData.current_song.title || 'Unknown Title',
            artist: stateData.current_song.artist || 'Unknown Artist',
            album: stateData.current_song.album || 'Unknown Album',
            duration: stateData.current_song.duration || 0,
            position: stateData.current_song.position || 0,
            file: stateData.current_song.file
          };
          this.playerStatus.position = Math.floor(stateData.current_song.position || 0);
        }
        // If neither condition is met, ignore the update to prevent data loss
      }
      
      // Emit events for changes
      if (oldState !== this.playerStatus.state) {
        this.emit(EventType.STATE_CHANGE, this.playerStatus.state);
      }
      
      if (oldVolume !== this.playerStatus.volume) {
        this.emit(EventType.VOLUME_CHANGE, this.playerStatus.volume);
      }
      
      if (oldPosition !== this.playerStatus.position) {
        this.emit(EventType.POSITION_CHANGE, this.playerStatus.position);
      }

      if (oldPlaylist !== this.playerStatus.playlist) {
        this.emit(EventType.PLAYLIST_CHANGE, this.playerStatus.playlist);
      }
    }

    // Connection methods
    async connect() {
      return new Promise((resolve, reject) => {
        try {
          this.emit(EventType.CONNECTION_CHANGE, ConnectionStatus.CONNECTING);
          
          // Create WebSocket URL for MQTT
          const wsUrl = `ws://${this.config.brokerUrl}:${this.config.port || 9001}`;
          
          // Create MQTT client
          this.mqttClient = mqtt.connect(wsUrl, {
            clientId: `web-client-${Math.random().toString(16).substr(2, 8)}`,
            keepalive: this.config.connectionOptions?.keepAlive || 60,
            clean: this.config.connectionOptions?.cleanSession !== false,
            reconnectPeriod: this.config.connectionOptions?.reconnectOnFailure ? 1000 : 0
          });

          this.mqttClient.on('connect', () => {
            console.log('Connected to MQTT broker');
            this.connectionStatus = ConnectionStatus.CONNECTED;
            this.emit(EventType.CONNECTION_CHANGE, this.connectionStatus);
            
            // Subscribe to device topics
            this.mqttClient.subscribe([
              this.topics.responses,
              this.topics.state,
              this.topics.connection
            ], (err) => {
              if (err) {
                console.error('Failed to subscribe to topics:', err);
                reject(err);
              } else {
                console.log('Subscribed to device topics');
                resolve();
              }
            });
          });

          this.mqttClient.on('message', (topic, message) => {
            this.handleMessage(topic, message);
          });

          this.mqttClient.on('error', (error) => {
            console.error('MQTT error:', error);
            this.connectionStatus = ConnectionStatus.ERROR;
            this.emit(EventType.CONNECTION_CHANGE, this.connectionStatus);
            this.emit(EventType.ERROR, error);
            reject(error);
          });

          this.mqttClient.on('close', () => {
            console.log('MQTT connection closed');
            this.connectionStatus = ConnectionStatus.DISCONNECTED;
            this.emit(EventType.CONNECTION_CHANGE, this.connectionStatus);
          });

        } catch (error) {
          reject(error);
        }
      });
    }

    async disconnect() {
      return new Promise((resolve) => {
        if (this.mqttClient) {
          this.mqttClient.end(false, {}, () => {
            this.mqttClient = null;
            this.connectionStatus = ConnectionStatus.DISCONNECTED;
            this.emit(EventType.CONNECTION_CHANGE, this.connectionStatus);
            resolve();
          });
        } else {
          resolve();
        }
      });
    }

    getConnectionStatus() {
      return this.connectionStatus;
    }

    // Player status methods
    getPlayerStatus() {
      return { ...this.playerStatus };
    }

    getPlaylists() {
      return [...this.playlists];
    }

    // Player control methods
    async play() {
      return this.sendCommand('play');
    }

    async pause() {
      return this.sendCommand('pause');
    }

    async stop() {
      return this.sendCommand('stop');
    }

    async next() {
      return this.sendCommand('next');
    }

    async previous() {
      return this.sendCommand('previous');
    }

    async setVolume(volume) {
      return this.sendCommand('volume', { volume });
    }

    async setRepeat(repeat) {
      return this.sendCommand('repeat', { repeat });
    }

    async setRandom(random) {
      return this.sendCommand('random', { random });
    }

    async getStatus() {
      const response = await this.sendCommand('status');
      if (response.data) {
        this.updatePlayerStatus(response.data);
      }
      return this.getPlayerStatus();
    }

    async fetchPlaylists() {
      const response = await this.sendCommand('playlists');
      // Handle device response format: response.data.result contains the array
      const playlistNames = response.data?.result || response.data;
      if (playlistNames && Array.isArray(playlistNames)) {
        this.playlists = playlistNames.map(name => ({
          name,
          items: [] // We'll need to fetch items separately if needed
        }));
        this.emit(EventType.PLAYLIST_CHANGE, this.playlists);
      }
      return this.getPlaylists();
    }

    async playPlaylist(playlistName) {
      return this.sendCommand('play_playlist', { playlist_name: playlistName });
    }

    async playTrack(trackIndex) {
      return this.sendCommand('play_track', { track_index: trackIndex });
    }

    async getPlaylistSongs(playlistName) {
      const response = await this.sendCommand('get_playlist_songs', { playlist_name: playlistName });
      return response.data?.result || [];
    }
  }

  // Export public API
  return {
    AmoraClient,
    PlayerState,
    ConnectionStatus,
    EventType
  };
})();
