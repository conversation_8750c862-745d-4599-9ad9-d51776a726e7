#!/bin/bash

# Start script for Amora Music Player

echo "Starting Amora Music Player..."

# Activate virtual environment
source /home/<USER>/venv/bin/activate

# Start MPD in the background
echo "Starting MPD..."
mpd /home/<USER>/app/config/mpd.conf

# Wait for MPD to start
sleep 2

# Update MPD database
echo "Updating MPD database..."
mpc update

# Start the Python application
echo "Starting Python application..."
cd /home/<USER>/app
python3 src/main.py
