FROM debian:bookworm

# Install dependencies
RUN apt-get update && apt-get install -y \
    mpd \
    mpc \
    alsa-utils \
    python3 \
    python3-pip \
    python3-venv \
    python3-full \
    python3-dev \
    curl \
    socat \
    nano \
    wget \
    sudo \
    net-tools \
    iputils-ping \
    procps \
    dbus \
    pulseaudio \
    pulseaudio-utils \
    psmisc \
    lsb-release \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure PulseAudio for network access (for dev mode)
RUN mkdir -p /etc/pulse
COPY edge/config/pulse-client.conf /etc/pulse/client.conf
COPY edge/config/pulse-system.pa /etc/pulse/system.pa

# Create user
RUN useradd -m -s /bin/bash user && \
    echo "user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/user

# Set up directories
RUN mkdir -p /home/<USER>/music/playlists \
    /home/<USER>/music/samples \
    /home/<USER>/app \
    /home/<USER>/.config/mpd \
    /home/<USER>/.local/share/mpd \
    /home/<USER>/logs

# Set permissions
RUN chown -R user:user /home/<USER>

# Switch to user
USER user
WORKDIR /home/<USER>/app

# Create a virtual environment
RUN python3 -m venv /home/<USER>/venv
ENV PATH="/home/<USER>/venv/bin:$PATH"
ENV VIRTUAL_ENV="/home/<USER>/venv"

# Copy pyproject.toml and poetry.lock
COPY --chown=user:user edge/pyproject.toml edge/poetry.lock* /home/<USER>/app/

# Install dependencies with pip in the virtual environment
RUN pip install --upgrade pip && \
    pip install click python-mpd2 asyncio pydantic azure-iot-device uvicorn fastapi

# Copy and install the AmoraSDK
COPY --chown=user:user sdk /tmp/amora-sdk
RUN cd /tmp/amora-sdk && pip install -e . && rm -rf /tmp/amora-sdk

# Copy application files
COPY --chown=user:user edge/src /home/<USER>/app/src
COPY --chown=user:user edge/config /home/<USER>/app/config
COPY --chown=user:user edge/scripts /home/<USER>/app/scripts

# Copy sample music files
COPY --chown=user:user edge/samples/*.mp3 /home/<USER>/music/samples/

# Make scripts executable
RUN chmod +x /home/<USER>/app/scripts/*.sh
RUN chmod +x /home/<USER>/app/scripts/*.py || true

# Configure network settings for better IoT connectivity
USER root
RUN echo "net.ipv4.tcp_keepalive_time = 60" >> /etc/sysctl.conf && \
    echo "net.ipv4.tcp_keepalive_intvl = 10" >> /etc/sysctl.conf && \
    echo "net.ipv4.tcp_keepalive_probes = 6" >> /etc/sysctl.conf

# Set up Pipewire
RUN mkdir -p /home/<USER>/.config/systemd/user
RUN mkdir -p /home/<USER>/.config/pipewire

# Copy start script from scripts directory to home directory
USER root
COPY --chown=user:user edge/scripts/start.sh /home/<USER>/start.sh
RUN chmod +x /home/<USER>/start.sh

USER user
WORKDIR /home/<USER>/app

CMD ["/home/<USER>/start.sh"]
