log_file "syslog"
music_directory "/home/<USER>/music"
playlist_directory "/home/<USER>/music/playlists"
db_file "/home/<USER>/.local/share/mpd/db"
state_file "/home/<USER>/.local/share/mpd/state"
pid_file "/home/<USER>/.local/share/mpd/pid"
bind_to_address "0.0.0.0"  # Allow connections from any IP
port "6600"
restore_paused "no"
auto_update "yes"
volume_normalization "no"

# Null output (always enabled)
audio_output {
    always_on "yes"
    enabled "yes"
    mixer_type "software"
    type "null"
    name "null-mpd"
}

# PulseAudio output (disabled in Linux mode)
audio_output {
    enabled "no"
    mixer_type "software"
    type "pulse"
    name "pulse-mpd"
    server "host.docker.internal"  # Windows host PulseAudio server
}

# ALSA output (fallback)
audio_output {
    enabled "yes"
    mixer_type "software"
    type "alsa"
    name "alsa-mpd"
    device "default"
}

# Pipewire output (enabled for Linux host)
audio_output {
    enabled "yes"
    mixer_type "software"
    type "pipewire"
    name "pipewire-mpd"
}

input {
    enabled "yes"
    plugin "curl"
}
