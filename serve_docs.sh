#!/bin/bash

# AmoraOS Documentation Server
# Serves versioned documentation using mike

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "Poetry is not installed. Please install Poetry and try again."
    echo "Installation instructions: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Change to the docs directory
cd docs

# Install dependencies if needed
if ! poetry check &> /dev/null; then
    echo "Setting up Poetry environment..."
    poetry install
fi

# Check if mike is available
if ! poetry run mike --help &> /dev/null; then
    echo "Installing mike for version management..."
    poetry add mike
fi

# Display available options
echo "AmoraOS Documentation Server"
echo "============================"
echo ""
echo "Available options:"
echo "  1. Serve versioned docs (recommended) - http://127.0.0.1:8000/"
echo "  2. Serve current v0.2 only - http://127.0.0.1:8001/"
echo "  3. Deploy new version"
echo "  4. List versions"
echo ""
read -p "Choose option (1-4) [1]: " choice
choice=${choice:-1}

case $choice in
    1)
        echo "Starting versioned documentation server..."
        echo "Available versions: v0.2 (latest), v0.1 (archive)"
        echo "Server: http://127.0.0.1:8000/"
        echo ""
        echo "Note: Livereload 404 errors are expected and harmless in mike serve mode."
        echo "For live reloading during development, use option 2 instead."
        echo ""
        echo "Press Ctrl+C to stop the server."
        poetry run mike serve --dev-addr=127.0.0.1:8000
        ;;
    2)
        echo "Starting current documentation server (v0.2 only)..."
        echo "Server: http://127.0.0.1:8001/"
        echo "Press Ctrl+C to stop the server."
        poetry run mkdocs serve --dev-addr=127.0.0.1:8001
        ;;
    3)
        echo "Available versions:"
        poetry run mike list
        echo ""
        read -p "Enter version to deploy (e.g., v0.3): " version
        read -p "Enter alias (e.g., latest): " alias
        if [ -n "$version" ]; then
            echo "Deploying version $version..."
            poetry run mike deploy --update-aliases "$version" "$alias"
            echo "Setting as default..."
            poetry run mike set-default "$version"
        fi
        ;;
    4)
        echo "Available versions:"
        poetry run mike list
        ;;
    *)
        echo "Invalid option. Starting versioned docs server..."
        poetry run mike serve --dev-addr=127.0.0.1:8000
        ;;
esac
