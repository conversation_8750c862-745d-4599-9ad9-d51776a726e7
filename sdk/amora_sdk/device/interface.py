"""
Player Interface module for AmoraSDK Device.

Provides a high-level interface for controlling the music player with error handling
and status callbacks.
"""

import logging
from typing import Dict, Any, List, Callable, Optional

try:
    from .player.music_player import MusicPlayer
except ImportError:
    # Fallback for direct imports in tests
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'player'))
    from music_player import MusicPlayer

logger = logging.getLogger(__name__)


class PlayerInterface:
    """High-level interface for controlling the music player."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Player Interface.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.player = MusicPlayer(config)
        self.connected = False
        self.status_callbacks: List[Callable[[Dict[str, Any]], None]] = []

    def connect(self) -> bool:
        """
        Connect to the music player.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.connect()
            self.connected = result
            if result:
                logger.info("Player interface connected successfully")
            else:
                logger.error("Failed to connect player interface")
            return result
        except Exception as e:
            logger.error(f"Exception during player interface connection: {e}")
            self.connected = False
            return False

    def disconnect(self) -> None:
        """Disconnect from the music player."""
        try:
            self.player.disconnect()
            logger.info("Player interface disconnected")
        except Exception as e:
            logger.error(f"Exception during player interface disconnection: {e}")
        finally:
            self.connected = False

    def play(self) -> bool:
        """
        Start or resume playback.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.play()
            if result:
                logger.info("Playback started via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during play: {e}")
            return False

    def pause(self) -> bool:
        """
        Pause playback.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.pause()
            if result:
                logger.info("Playback paused via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during pause: {e}")
            return False

    def stop(self) -> bool:
        """
        Stop playback.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.stop()
            if result:
                logger.info("Playback stopped via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during stop: {e}")
            return False

    def next(self) -> bool:
        """
        Skip to next track.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.next()
            if result:
                logger.info("Skipped to next track via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during next: {e}")
            return False

    def previous(self) -> bool:
        """
        Skip to previous track.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.previous()
            if result:
                logger.info("Skipped to previous track via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during previous: {e}")
            return False

    def set_volume(self, volume: int) -> bool:
        """
        Set volume level.

        Args:
            volume (int): Volume level (0-100)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.set_volume(volume)
            if result:
                logger.info(f"Volume set to {volume} via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during set_volume: {e}")
            return False

    def get_volume(self) -> int:
        """
        Get current volume level.

        Returns:
            int: Current volume level (0-100)
        """
        try:
            return self.player.get_volume()
        except Exception as e:
            logger.error(f"Exception during get_volume: {e}")
            return 0

    def get_status(self) -> Dict[str, Any]:
        """
        Get current player status.

        Returns:
            Dict[str, Any]: Player status
        """
        try:
            return self.player.get_status()
        except Exception as e:
            logger.error(f"Exception during get_status: {e}")
            return {
                "state": "unknown",
                "volume": 0,
                "current_song": None,
                "playlist": None,
                "repeat": False,
                "random": False
            }

    def get_playlists(self) -> List[str]:
        """
        Get available playlists.

        Returns:
            List[str]: List of playlist names
        """
        try:
            return self.player.get_playlists()
        except Exception as e:
            logger.error(f"Exception during get_playlists: {e}")
            return []

    def update_database(self) -> bool:
        """
        Update player database.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.update_database()
            if result:
                logger.info("Database updated via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during update_database: {e}")
            return False

    def play_playlist(self, playlist_name: str) -> bool:
        """
        Play a playlist.

        Args:
            playlist_name (str): Name of the playlist

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.play_playlist(playlist_name)
            if result:
                logger.info(f"Playing playlist '{playlist_name}' via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during play_playlist: {e}")
            return False

    def play_track(self, track_index: int) -> bool:
        """
        Play a specific track by index.

        Args:
            track_index (int): Index of the track to play (0-based)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.play_track(track_index)
            if result:
                logger.info(f"Playing track at index {track_index} via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during play_track: {e}")
            return False

    def get_playlist_songs(self, playlist_name: str) -> List[Dict[str, Any]]:
        """
        Get songs in a playlist.

        Args:
            playlist_name (str): Name of the playlist

        Returns:
            List[Dict[str, Any]]: List of songs in the playlist
        """
        try:
            result = self.player.get_playlist_songs(playlist_name)
            logger.info(f"Retrieved {len(result)} songs from playlist '{playlist_name}' via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during get_playlist_songs: {e}")
            return []

    def set_repeat(self, repeat: bool) -> bool:
        """
        Set repeat mode.

        Args:
            repeat (bool): Whether to enable repeat mode

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.set_repeat(repeat)
            if result:
                logger.info(f"Repeat mode set to {repeat} via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during set_repeat: {e}")
            return False

    def set_random(self, random: bool) -> bool:
        """
        Set random mode.

        Args:
            random (bool): Whether to enable random mode

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            result = self.player.set_random(random)
            if result:
                logger.info(f"Random mode set to {random} via interface")
            return result
        except Exception as e:
            logger.error(f"Exception during set_random: {e}")
            return False

    def register_status_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Register a callback for status updates.

        Args:
            callback: Function to call when status updates occur
        """
        if callback not in self.status_callbacks:
            self.status_callbacks.append(callback)
            logger.info("Status callback registered")

    def unregister_status_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Unregister a status callback.

        Args:
            callback: Function to remove from callbacks
        """
        if callback in self.status_callbacks:
            self.status_callbacks.remove(callback)
            logger.info("Status callback unregistered")

    def notify_status_update(self, status: Dict[str, Any]) -> None:
        """
        Notify all registered callbacks of a status update.

        Args:
            status: Status dictionary to send to callbacks
        """
        for callback in self.status_callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"Exception in status callback: {e}")
