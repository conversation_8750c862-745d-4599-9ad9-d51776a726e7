# AmoraOS Complete System Setup Guide

This guide provides step-by-step instructions to set up and run the complete AmoraOS system, including the device (edge), MQTT broker, and web player components.

## System Overview

The AmoraOS system consists of three main components:

1. **Edge Device** - Raspberry Pi running the music player with SDK integration
2. **MQTT Broker** - Real-time communication hub (can be local or cloud-based)
3. **Web Player** - Browser-based interface for controlling the music player

## Prerequisites

### Hardware Requirements
- Raspberry Pi 4 (recommended) or Raspberry Pi 3B+
- IQUADIO PI DA audio HAT (optional but recommended for high-quality audio)
- SD card (16GB or larger)
- Power supply for Raspberry Pi
- Speakers or headphones
- Network connection (WiFi or Ethernet)

### Software Requirements
- **Development Machine:**
  - Linux system (Windows development is no longer supported)
  - Python 3.10 or higher
  - Poetry for dependency management
  - Docker (for containerized deployment)
  - Git

- **Raspberry Pi:**
  - Debian Bookworm
  - Docker (for containerized deployment)

## Component Setup

### 1. MQTT Broker Setup

You have two options for the MQTT broker:

#### Option A: Local MQTT Broker (<PERSON>squitto)

Install Mosquitto on your development machine or a dedicated server:

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mosquitto mosquitto-clients

# Start the service
sudo systemctl start mosquitto
sudo systemctl enable mosquitto

# Test the broker
mosquitto_pub -h localhost -t test/topic -m "Hello MQTT"
mosquitto_sub -h localhost -t test/topic
```

#### Option B: Cloud MQTT Broker

Use a cloud service like:
- HiveMQ Cloud
- AWS IoT Core
- Azure IoT Hub
- Eclipse IoT

### 2. SDK Setup and Testing

First, set up the SDK and run tests to ensure everything works:

```bash
# Clone the repository
git clone <repository-url>
cd amora-os

# Set up the SDK
cd sdk
poetry install

# Run all tests to verify setup
./run_tests.sh
```

All tests should pass (130 tests). If any tests fail, check the error messages and ensure all dependencies are installed.

### 3. Edge Device Setup

#### Development Mode (Local Testing)

For development and testing on your local machine:

```bash
# Navigate to edge directory
cd ../edge

# Install dependencies
poetry install

# Configure the device
cp config/config.json config/config.dev.json
# Edit config.dev.json with your settings

# Build Docker image
docker build -t waybox-python-player .

# Run in development mode
docker run -d --name waybox-player-dev --privileged --network host \
  -v ./samples:/home/<USER>/music \
  -v ./config:/home/<USER>/app/config \
  --device /dev/snd:/dev/snd \
  --group-add audio \
  waybox-python-player python3 src/main.py
```

#### Production Mode (Raspberry Pi)

For deployment on Raspberry Pi:

```bash
# On the Raspberry Pi, clone the repository
git clone <repository-url>
cd amora-os/edge

# Build the Docker image
docker build -t waybox-python-player .

# Create music directory
sudo mkdir -p /home/<USER>/music
sudo chown -R $USER:$USER /home/<USER>/music

# Copy your music files to /home/<USER>/music

# Run the container
docker run -d --name waybox-player --privileged --network host \
  -v /home/<USER>/music:/home/<USER>/music \
  --device /dev/snd:/dev/snd \
  --group-add audio \
  --restart unless-stopped \
  waybox-python-player
```

### 4. Web Player Setup

The web player provides a browser-based interface:

```bash
# Navigate to web player directory
cd ../web-player

# Serve the web application (choose one method)

# Method 1: Python HTTP server
python3 -m http.server 8080

# Method 2: Node.js serve (if you have Node.js)
npx serve -s . -l 8080

# Method 3: Any other web server
# Copy files to your web server's document root
```

Access the web player at `http://localhost:8080` (or your server's IP).

## Configuration

### MQTT Configuration

Create or edit the MQTT configuration file for the SDK test application:

```bash
cd sdk
cp test_app/mqtt_test/credentials_template.json credentials_configs.txt
```

Edit `credentials_configs.txt`:

```json
{
    "mqtt": {
        "broker_url": "localhost",
        "port": 1883,
        "username": "",
        "password": "",
        "device_id": "amora-player-001",
        "topic_prefix": "amora/devices",
        "use_tls": false,
        "keep_alive": 60,
        "clean_session": true,
        "reconnect_on_failure": true,
        "max_reconnect_delay": 300,
        "default_qos": 1
    },
    "player": {
        "mpd_host": "localhost",
        "mpd_port": 6600,
        "storage_path": "/home/<USER>/music",
        "playlists_path": "/home/<USER>/music/playlists",
        "audio_backend": "pipewire",
        "audio_device": "default",
        "audio_volume": 80,
        "dev_mode": true
    }
}
```

### Edge Device Configuration

Edit `edge/config/config.json`:

```json
{
    "device": {
        "id": "amora-player-001",
        "name": "Amora Music Player"
    },
    "mpd": {
        "host": "localhost",
        "port": 6600
    },
    "content": {
        "storage_path": "/home/<USER>/music",
        "playlists_path": "/home/<USER>/music/playlists"
    },
    "audio": {
        "backend": "pipewire",
        "device": "default",
        "volume": 80
    },
    "broker": {
        "broker_url": "localhost",
        "port": 1883,
        "use_tls": false,
        "device_id": "amora-player-001"
    },
    "dev_mode": false
}
```

## Manual System Startup Guide

Follow these steps to manually start all components of the AmoraOS system:

### Step 1: Configure MQTT Broker for WebSocket Support

First, configure Mosquitto to support both regular MQTT and WebSocket connections:

```bash
# Add WebSocket configuration to Mosquitto
sudo tee -a /etc/mosquitto/mosquitto.conf << 'EOF'

# WebSocket listener for web clients
listener 9001
protocol websockets
allow_anonymous true

# Regular MQTT listener
listener 1883
allow_anonymous true
EOF

# Restart Mosquitto to apply changes
sudo systemctl restart mosquitto

# Verify both ports are listening
sudo netstat -tlnp | grep mosquitto
# Should show ports 1883 and 9001
```

### Step 2: Prepare SDK Configuration

Create the credentials configuration file:

```bash
cd sdk

# Create MQTT credentials configuration
cat > credentials_configs.txt << 'EOF'
{
    "mqtt": {
        "broker_url": "localhost",
        "port": 1883,
        "username": "",
        "password": "",
        "device_id": "amora-player-001",
        "topic_prefix": "amora/devices",
        "use_tls": false,
        "keep_alive": 60,
        "clean_session": true,
        "reconnect_on_failure": true,
        "max_reconnect_delay": 300,
        "default_qos": 1
    },
    "player": {
        "mpd_host": "localhost",
        "mpd_port": 6600,
        "storage_path": "/home/<USER>/music",
        "playlists_path": "/home/<USER>/music/playlists",
        "audio_backend": "pipewire",
        "audio_device": "default",
        "audio_volume": 80,
        "dev_mode": true
    }
}
EOF
```

### Step 3: Build and Start Edge Device Container

```bash
cd ../edge

# Build the Docker image (if not already built)
docker build -t waybox-python-player .

# Remove any existing container
docker stop waybox-player-dev 2>/dev/null || true
docker rm waybox-player-dev 2>/dev/null || true

# Start the container with SDK mounted
docker run -d --name waybox-player-dev --privileged --network host \
  -v ./samples:/home/<USER>/music \
  -v ./config:/home/<USER>/app/config \
  -v ../sdk:/home/<USER>/app/sdk \
  --device /dev/snd:/dev/snd \
  --group-add audio \
  waybox-python-player bash -c "cd /home/<USER>/app && PYTHONPATH=/home/<USER>/app/sdk:\$PYTHONPATH source /home/<USER>/venv/bin/activate && /home/<USER>/start.sh"

# Check if container is running
docker ps

# Check container logs
docker logs -f waybox-player-dev
```

### Step 4: Start MQTT Monitor (Optional but Recommended)

Open a new terminal to monitor MQTT traffic:

```bash
# Monitor all MQTT messages for debugging
mosquitto_sub -h localhost -t "amora/devices/+/+" -v
```

Keep this running to see real-time MQTT communication between components.

### Step 5: Start Web Player

```bash
cd web-player

# Start web server (try different ports if 8080 is in use)
python3 -m http.server 8081

# Alternative ports if needed:
# python3 -m http.server 8082
# python3 -m http.server 8083
```

### Step 6: Access and Configure Web Player

1. **Open Web Player**: Navigate to `http://localhost:8081` in your browser

2. **Configure Connection**:
   - **Broker URL**: `localhost`
   - **Port**: `9001` (WebSocket MQTT port)
   - **Device ID**: `amora-player-001`

3. **Connect**: Click the "Connect" button

4. **Test Controls**: Try the play/pause, volume, and navigation controls

### Step 7: Test Manual MQTT Commands (Optional)

You can also send manual MQTT commands to test the device:

```bash
# Test play command
mosquitto_pub -h localhost -t "amora/devices/amora-player-001/commands" \
  -m '{"command":"play","command_id":"test-123","timestamp":'$(date +%s)'}'

# Test status command
mosquitto_pub -h localhost -t "amora/devices/amora-player-001/commands" \
  -m '{"command":"status","command_id":"test-124","timestamp":'$(date +%s)'}'

# Test volume command
mosquitto_pub -h localhost -t "amora/devices/amora-player-001/commands" \
  -m '{"command":"volume","command_id":"test-125","params":{"volume":50},"timestamp":'$(date +%s)'}'
```

### Step 8: Troubleshoot Container Issues

If the container stops or has issues:

```bash
# Access the container interactively
docker exec -it waybox-player-dev bash

# Inside the container, manually start services:
# 1. Start MPD
mpd config/mpd.conf

# 2. Wait for MPD to start
sleep 3

# 3. Update music database
mpc update

# 4. Check MPD status
mpc status

# 5. Start the Python application
source /home/<USER>/venv/bin/activate
PYTHONPATH=/home/<USER>/app/sdk:$PYTHONPATH python3 src/main.py
```

## System Status Verification

### Check All Components Are Running

```bash
# 1. MQTT Broker
sudo systemctl status mosquitto
sudo netstat -tlnp | grep mosquitto

# 2. Docker Container
docker ps
docker logs waybox-player-dev

# 3. Web Server
# Check if http://localhost:8081 is accessible

# 4. MQTT Communication
# Check the mosquitto_sub terminal for message traffic
```

### Expected MQTT Message Flow

When everything is working, you should see these message types in the MQTT monitor:

1. **Commands** (from web player to device):
   ```
   amora/devices/amora-player-001/commands {"command":"play","command_id":"web-123","timestamp":1234567890}
   ```

2. **Responses** (from device to web player):
   ```
   amora/devices/amora-player-001/responses {"command_id":"web-123","result":true,"message":"Command executed"}
   ```

3. **State Updates** (from device to web player):
   ```
   amora/devices/amora-player-001/state {"state":"play","volume":50,"current_song":{"title":"Song Title"}}
   ```

## Quick Restart Commands

If you need to restart everything quickly:

```bash
# Restart MQTT broker
sudo systemctl restart mosquitto

# Restart Docker container
docker restart waybox-player-dev

# Restart web server (Ctrl+C in the web server terminal, then):
cd web-player && python3 -m http.server 8081
```

## Raspberry Pi Specific Setup

### Audio Configuration

For the IQUADIO PI DA audio HAT:

```bash
# Add to /boot/config.txt
dtoverlay=iqaudio-dac

# Reboot
sudo reboot

# Test audio
aplay /usr/share/sounds/alsa/Front_Left.wav
```

### Auto-start on Boot

Create a systemd service:

```bash
sudo nano /etc/systemd/system/amora-player.service
```

```ini
[Unit]
Description=Amora Music Player
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/bin/docker start waybox-player
ExecStop=/usr/bin/docker stop waybox-player
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
```

Enable the service:

```bash
sudo systemctl enable amora-player.service
sudo systemctl start amora-player.service
```

## Troubleshooting

### Common Issues and Solutions

#### 1. **Container Stops Immediately**
```bash
# Check container logs
docker logs waybox-player-dev

# Common causes:
# - MPD not starting: Check MPD configuration
# - Python import errors: Ensure SDK is mounted correctly
# - Audio device issues: Check --device /dev/snd mapping
```

#### 2. **Web Player Can't Connect**
```bash
# Check if Mosquitto has WebSocket support
sudo netstat -tlnp | grep 9001

# If port 9001 is not listening, reconfigure Mosquitto:
sudo systemctl restart mosquitto

# Check browser console (F12) for WebSocket errors
```

#### 3. **No Audio Output**
```bash
# Inside container, test audio
docker exec -it waybox-player-dev bash
aplay -l  # List audio devices
mpc status  # Check MPD status
mpc outputs  # Check MPD audio outputs
mpc enable 1  # Enable audio output
```

#### 4. **MQTT Commands Not Working**
```bash
# Test MQTT broker connectivity
mosquitto_pub -h localhost -t test -m "hello"
mosquitto_sub -h localhost -t test

# Check device is subscribed to commands topic
# Look for subscription messages in container logs
```

#### 5. **Web Player Shows "No Tracks Available"**
```bash
# Check if music files are mounted correctly
docker exec -it waybox-player-dev ls -la /home/<USER>/music/

# Update MPD database
docker exec -it waybox-player-dev mpc update

# Check MPD can see the files
docker exec -it waybox-player-dev mpc listall
```

#### 6. **Port Already in Use Errors**
```bash
# Find what's using the port
sudo lsof -i :8081  # For web server
sudo lsof -i :9001  # For MQTT WebSocket
sudo lsof -i :1883  # For MQTT

# Kill the process or use different ports
```

#### 7. **Docker Build Failures**
```bash
# Clean Docker cache
docker system prune -a

# Rebuild from scratch
docker build --no-cache -t waybox-python-player .
```

#### 8. **Permission Denied Errors**
```bash
# Fix audio group permissions
sudo usermod -aG audio $USER

# Fix Docker permissions
sudo usermod -aG docker $USER

# Logout and login again for group changes to take effect
```

### Logs and Debugging

```bash
# Check container logs
docker logs waybox-player-dev

# Check MQTT broker logs
sudo journalctl -u mosquitto -f

# Check system audio
pactl info  # For PipeWire/PulseAudio
aplay -l     # List audio devices
```

### Port Usage

- MQTT Broker: 1883 (default), 8883 (TLS)
- Web Player: 8080 (configurable)
- MPD: 6600 (internal to container)

## Next Steps

Once the system is running:

1. Add your music files to the music directory
2. Create playlists in the playlists directory
3. Test all functionality through both MQTT client and web interface
4. Configure auto-start for production deployment
5. Set up monitoring and logging as needed

For advanced configuration and development, refer to the individual component documentation in their respective directories.

## System Validation

### Testing the Complete System

After setting up all components, validate the system works end-to-end:

#### 1. Verify MQTT Communication

```bash
# Test MQTT broker connectivity
mosquitto_pub -h localhost -t "amora/devices/amora-player-001/commands" -m '{"command":"status","command_id":"test-123","timestamp":1234567890}'

# Subscribe to responses
mosquitto_sub -h localhost -t "amora/devices/amora-player-001/responses"
```

#### 2. Test Edge Device Integration

```bash
# Check if the edge device container is running
docker ps | grep waybox-player

# Test audio playback
docker exec -it waybox-player-dev bash
# Inside container:
mpc update
mpc add /home/<USER>/music/
mpc play
```

#### 3. Validate Web Player

1. Open web player in browser
2. Configure MQTT connection settings
3. Click "Connect"
4. Test player controls (play, pause, volume)
5. Verify real-time status updates

### Integration Test Scenarios

#### Scenario 1: Basic Playback Control
1. Start music via web player
2. Verify audio output from Raspberry Pi
3. Change volume via MQTT client
4. Confirm volume change in web player

#### Scenario 2: Playlist Management
1. Add music files to `/home/<USER>/music/`
2. Update database via MQTT command
3. Load playlist via web interface
4. Verify playback starts

#### Scenario 3: Real-time Status Updates
1. Start playback on device
2. Monitor status updates in web player
3. Skip tracks via MQTT client
4. Confirm UI updates in real-time

## Performance Considerations

### Resource Usage
- **RAM**: ~200MB for edge device container
- **CPU**: Low usage during playback, spikes during database updates
- **Network**: Minimal MQTT traffic (~1KB/s for status updates)
- **Storage**: Depends on music library size

### Optimization Tips
1. Use SSD storage for better performance on Raspberry Pi
2. Configure MQTT QoS levels based on reliability needs
3. Adjust status update intervals to balance responsiveness vs. network usage
4. Use audio HAT for better sound quality and reduced CPU load

## Security Considerations

### MQTT Security
```bash
# Enable authentication in Mosquitto
sudo nano /etc/mosquitto/mosquitto.conf

# Add:
allow_anonymous false
password_file /etc/mosquitto/passwd

# Create user
sudo mosquitto_passwd -c /etc/mosquitto/passwd amora_user
sudo systemctl restart mosquitto
```

### Network Security
- Use TLS for MQTT in production
- Configure firewall rules
- Use VPN for remote access
- Regular security updates

## Monitoring and Maintenance

### Log Management
```bash
# Rotate Docker logs
echo '{"log-driver":"json-file","log-opts":{"max-size":"10m","max-file":"3"}}' | sudo tee /etc/docker/daemon.json
sudo systemctl restart docker

# Monitor system resources
htop
iotop
```

### Health Checks
```bash
# Create health check script
cat > /usr/local/bin/amora-health-check.sh << 'EOF'
#!/bin/bash
# Check if container is running
if ! docker ps | grep -q waybox-player; then
    echo "ERROR: Amora player container not running"
    exit 1
fi

# Check MQTT connectivity
if ! mosquitto_pub -h localhost -t test -m "health" -q 1; then
    echo "ERROR: MQTT broker not accessible"
    exit 1
fi

echo "OK: All services healthy"
EOF

chmod +x /usr/local/bin/amora-health-check.sh
```

### Backup and Recovery
```bash
# Backup configuration
tar -czf amora-backup-$(date +%Y%m%d).tar.gz \
    /home/<USER>/music/playlists \
    edge/config/ \
    sdk/credentials_configs.txt

# Restore from backup
tar -xzf amora-backup-YYYYMMDD.tar.gz
```

## Quick Reference Commands

### Essential Commands for Daily Use

```bash
# Start everything from scratch
sudo systemctl restart mosquitto
docker restart waybox-player-dev
cd web-player && python3 -m http.server 8081

# Monitor MQTT traffic
mosquitto_sub -h localhost -t "amora/devices/+/+" -v

# Check system status
docker ps
sudo systemctl status mosquitto
curl -s http://localhost:8081 > /dev/null && echo "Web server OK" || echo "Web server DOWN"

# Access container for debugging
docker exec -it waybox-player-dev bash

# Test MQTT manually
mosquitto_pub -h localhost -t "amora/devices/amora-player-001/commands" \
  -m '{"command":"status","command_id":"manual-test","timestamp":'$(date +%s)'}'

# Check container logs
docker logs -f waybox-player-dev

# Restart web server on different port
cd web-player && python3 -m http.server 8082
```

### Component URLs and Ports

- **Web Player**: `http://localhost:8081`
- **MQTT Broker**: `localhost:1883` (regular) / `localhost:9001` (WebSocket)
- **Device ID**: `amora-player-001`
- **MQTT Topics**:
  - Commands: `amora/devices/amora-player-001/commands`
  - Responses: `amora/devices/amora-player-001/responses`
  - State: `amora/devices/amora-player-001/state`

### File Locations

- **SDK**: `sdk/`
- **Edge Device**: `edge/`
- **Web Player**: `web-player/`
- **Music Files**: `edge/samples/` (mounted to `/home/<USER>/music` in container)
- **Configuration**: `edge/config/` (mounted to `/home/<USER>/app/config` in container)
- **MQTT Config**: `sdk/credentials_configs.txt`

This completes the comprehensive setup guide for the AmoraOS system. The guide covers everything from initial setup to production deployment, testing, and maintenance.
