site_name: AmoraOS MVP 1
site_description: Raspberry Pi music box with local Web Control UI over MQTT
site_author: AmoraOS Team

# Version
extra:
  version:
    provider: mike
    default: v0.2
  versions:
    - version: v0.2
      title: v0.2 (MVP 1)
      aliases: [latest]
    - version: v0.1
      title: v0.1 (Archive)
      aliases: [archive]

# Repository
repo_name: amora-os
repo_url: https://github.com/yourusername/amora-os

# Directories
docs_dir: site_docs
site_dir: ../site

# Navigation
nav:
  - Home: index.md
  - Quick Start: quick-start.md
  - API Reference: api-reference.md
  - Development: development.md

# Theme
theme:
  name: material
  palette:
    primary: indigo
    accent: indigo
  features:
    - navigation.instant
    - navigation.tracking
    - navigation.expand
    - navigation.indexes
    - content.code.copy

# Extensions
markdown_extensions:
  - admonition
  - codehilite
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.details
  - toc:
      permalink: true

# Plugins
plugins:
  - search
