# Development Guide

Contributing to and extending AmoraOS MVP 1.

## Development Environment

### Prerequisites

- **Ubuntu/Linux** development machine
- **Docker** and **Docker Compose**
- **Python 3.9+** with Poetry
- **Node.js 18+** for Web UI
- **VS Code** with Augment/Claude extensions (recommended)

### Setup

```bash
# Clone repository
git clone https://github.com/yourusername/amora-os.git
cd amora-os

# Install Python dependencies
cd sdk
poetry install
poetry shell

# Install Web UI dependencies  
cd ../web-ui
npm install

# Start development services
docker compose -f docker-compose.dev.yml up -d
```

## Project Structure

```
amora-os/
├── sdk/                    # Python SDK
│   ├── amora_sdk/         # Core SDK code
│   ├── tests/             # Unit tests
│   └── pyproject.toml     # Poetry config
├── web-ui/                # React Web UI
│   ├── src/               # UI source code
│   ├── public/            # Static assets
│   └── package.json       # npm config
├── edge/                  # Raspberry Pi deployment
│   ├── Dockerfile         # Container definition
│   └── config/            # Device configuration
├── docs/                  # Documentation
└── docker-compose.yml     # MQTT broker setup
```

## Core Components

### 1. AmoraSDK (Python)

**Location:** `sdk/amora_sdk/`

Key modules:
- `device/` - Device-side implementation
- `broker/` - MQTT communication
- `player/` - MPD integration

**Testing:**
```bash
cd sdk
poetry run pytest --cov=amora_sdk tests/
```

### 2. Web UI (React)

**Location:** `web-ui/`

Key features:
- Real-time MQTT.js integration
- Device discovery via mDNS
- Responsive design for mobile/desktop

**Development:**
```bash
cd web-ui
npm run dev  # Start development server
npm test     # Run tests
```

### 3. Edge Container

**Location:** `edge/`

Raspberry Pi deployment container with:
- MPD + PipeWire audio stack
- AmoraSDK device service
- Heartbeat monitoring

## Development Workflow

### 1. Context-Engineering Strategy

We use AI-assisted development with structured context:

- **Static docs** - `docs/ARCHITECTURE.md`, `API_SPEC.md`
- **Sprint files** - `SPRINT_PLAN.md`, `OPEN_ISSUES.md`  
- **AI guidance** - `ai/AI_RULES.md`, `.claude.md`

### 2. Task Management

Tasks are managed just-in-time using:
- Augment's task queue
- Claude sessions for scoping
- GitHub Issues for tracking

### 3. Definition of Done

1. ✅ Code merged to `main` with green CI
2. ✅ Public API & docs updated
3. ✅ Tests written/updated (≥80% coverage)
4. ✅ Context files refreshed

## Testing Strategy

### Unit Tests

```bash
# SDK tests
cd sdk && poetry run pytest

# Web UI tests  
cd web-ui && npm test

# Integration tests
./run-integration-tests.sh
```

### Performance Tests

Key metrics to validate:

| Metric | Target | Test Command |
|--------|--------|--------------|
| Play latency | ≤300ms | `./test-latency.sh play` |
| Status sync | ≤200ms | `./test-latency.sh status` |
| Heartbeat jitter | ±5s | `./test-heartbeat.sh` |

### Manual Testing

1. **Device Discovery:**
   - Pi appears in UI within 5s
   - mDNS resolution works

2. **Playback Control:**
   - All buttons respond instantly
   - Audio starts within 300ms
   - Volume changes are immediate

3. **State Synchronization:**
   - Multiple UI clients stay in sync
   - Reconnection after network loss

## Extending AmoraOS

### Adding New Commands

1. **Define command in SDK:**
   ```python
   # sdk/amora_sdk/device/commands.py
   async def shuffle(self, enabled: bool):
       """Enable/disable shuffle mode."""
       await self._mpd_client.shuffle(enabled)
   ```

2. **Add MQTT handler:**
   ```python
   # sdk/amora_sdk/device/broker/manager.py
   self.register_handler("shuffle", self.shuffle)
   ```

3. **Update Web UI:**
   ```javascript
   // web-ui/src/components/Controls.jsx
   const handleShuffle = () => {
       client.publish(`amora/devices/${deviceId}/commands/shuffle`, 
                     JSON.stringify({enabled: !shuffleEnabled}));
   };
   ```

### Adding New Status Fields

1. **Extend status schema:**
   ```python
   # sdk/amora_sdk/models/status.py
   class PlayerStatus(BaseModel):
       # ... existing fields
       shuffle_enabled: bool = False
   ```

2. **Update heartbeat:**
   ```python
   # sdk/amora_sdk/device/heartbeat.py
   status.shuffle_enabled = await self.player.get_shuffle()
   ```

## Debugging

### MQTT Message Tracing

```bash
# Monitor all MQTT traffic
mosquitto_sub -h localhost -t "amora/#" -v

# Monitor specific device
mosquitto_sub -h localhost -t "amora/devices/player-001/#" -v
```

### Device Logs

```bash
# SSH to Raspberry Pi
ssh pi@your-pi-ip

# Check container logs
docker logs amora-device -f

# Check system audio
aplay -l
systemctl status pipewire
```

### Performance Profiling

```bash
# Profile SDK performance
cd sdk
poetry run python -m cProfile -o profile.stats test_performance.py

# Analyze results
poetry run python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"
```

## Contributing

1. **Fork** the repository
2. **Create** feature branch: `git checkout -b feature/new-command`
3. **Implement** changes with tests
4. **Update** documentation
5. **Submit** pull request

### Code Style

- **Python:** Black formatter, isort imports
- **JavaScript:** Prettier, ESLint
- **Commit messages:** Conventional commits format

```bash
# Format code
cd sdk && poetry run black . && poetry run isort .
cd web-ui && npm run format
```
