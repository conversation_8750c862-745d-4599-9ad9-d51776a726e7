# Quick Start Guide

Get AmoraOS MVP 1 running in under 10 minutes.

## Prerequisites

- **Raspberry Pi 4** (recommended) or Pi 3B+
- **SD card** (16GB or larger) with Raspberry Pi OS
- **Network connection** (WiFi or Ethernet)
- **Audio output** (speakers, headphones, or DAC)
- **Development machine** (Ubuntu/Linux preferred)

## Step 1: Prepare Raspberry Pi

1. **Flash Raspberry Pi OS** to SD card
2. **Enable SSH** and configure WiFi (if needed)
3. **Boot Pi** and note its IP address

```bash
# Find your Pi on the network
nmap -sn ***********/24 | grep -i raspberry
```

## Step 2: Clone Repository

```bash
git clone https://github.com/yourusername/amora-os.git
cd amora-os
```

## Step 3: Start MQTT Broker

```bash
# Start Mosquitto broker locally
docker compose up -d mosquitto

# Verify broker is running
docker compose ps
```

## Step 4: Deploy to Raspberry Pi

```bash
# Configure deployment
cp config/deploy.example.yml config/deploy.yml
# Edit config/deploy.yml with your Pi's IP

# Deploy AmoraOS
./deploy.sh
```

## Step 5: Verify Installation

1. **Check device heartbeat:**
   ```bash
   # Subscribe to heartbeat topic
   mosquitto_sub -h localhost -t "amora/devices/+/heartbeat"
   ```

2. **Test basic commands:**
   ```bash
   # Send play command
   mosquitto_pub -h localhost -t "amora/devices/your-device-id/commands/play" -m "{}"
   
   # Check status
   mosquitto_sub -h localhost -t "amora/devices/your-device-id/status" -C 1
   ```

## Step 6: Open Web UI

1. **Navigate to Web UI:**
   ```
   http://your-pi-ip:3000
   ```

2. **Test controls:**
   - Play/Pause buttons
   - Volume slider
   - Track information display

## Success Verification

Your installation is successful when:

- ✅ Device appears in Web UI within 5 seconds
- ✅ Play command produces audio within 300ms
- ✅ UI state updates within 200ms
- ✅ Heartbeat appears every 30 seconds

## Troubleshooting

### No Audio Output
```bash
# Check audio devices
aplay -l

# Test audio directly
speaker-test -t wav -c 2
```

### MQTT Connection Issues
```bash
# Check broker logs
docker compose logs mosquitto

# Test broker connectivity
mosquitto_pub -h localhost -t "test" -m "hello"
```

### Device Not Discovered
```bash
# Check mDNS
avahi-browse -rt _amora._tcp

# Check device logs
ssh pi@your-pi-ip "docker logs amora-device"
```

## Next Steps

- [API Reference](api-reference.md) - Learn the SDK and MQTT commands
- [Development](development.md) - Extend and customize AmoraOS
