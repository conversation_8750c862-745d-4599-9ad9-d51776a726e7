# AmoraOS MVP 1

**A Raspberry Pi music box with local Web Control UI over MQTT**

## Quick Start

Get your first track playing in under 10 minutes:

```bash
# 1. Clone the repository
git clone https://github.com/yourusername/amora-os.git
cd amora-os

# 2. Start the MQTT broker
docker compose up -d mosquitto

# 3. Deploy to Raspberry Pi
./deploy.sh

# 4. Open Web UI
# Navigate to http://your-pi-ip:3000
```

## What is AmoraOS MVP 1?

AmoraOS MVP 1 is a **seed release** focused on core functionality:

- **Raspberry Pi music player** with MPD + PipeWire
- **Local Web Control UI** for play/pause/volume control  
- **MQTT communication** over single Mosquitto broker on LAN
- **Near-instant response** (≤300ms play command to audio)
- **Real-time state sync** (≤200ms UI updates)
- **Device heartbeat** (JSON every 30s)

## Success Criteria

| Measure | Target |
|---------|--------|
| <PERSON> auto-joins LAN & discovered via mDNS | ≤ 5s |
| Play command → audio heard | ≤ 300ms |
| UI state sync round-trip | ≤ 200ms |
| Heartbeat JSON interval | every 30s (±5s) |
| `git clone` → first track playing | ≤ 10 min |
| Unit-test coverage (core) | ≥ 80% |

## Architecture Overview

```text
┌──────── Dev Host (Ubuntu) ──────────┐
│ VS Code + Augment / Claude CLI      │
│ Web Control SPA (React + MQTT.js)  │
└───────────────┬─────────────────────┘
                │  MQTT
                ▼
      ┌────── Mosquitto Broker ───────┐
      │  docker compose service       │
      └───────────────┬───────────────┘
                      │
           MQTT cmd / state topics
                      │
┌────────────── Raspberry Pi ───────────────┐
│ AmoraOS container                         │
│  ├─ amora-sdk  → MPD → PipeWire → DAC     │
│  ├─ heartbeat.py  (publish JSON)          │
│  └─ structured-logger                     │
└───────────────────────────────────────────┘
```

## MVP Scope

**In Scope:**
- MPD + PipeWire playback
- MQTT control plane (single broker)
- Python SDK (`play`, `pause`, `status`, `set_volume`)
- Device heartbeat JSON schema
- Local Web UI dashboard (same LAN)

**Deferred to Future:**
- Multi-room sync
- mTLS & zero-touch provisioning
- Advanced offline cache/eviction
- Cloud fleet console
- AI audio analysis sidecar

## Next Steps

1. [Quick Start Guide](quick-start.md) - Get up and running
2. [API Reference](api-reference.md) - SDK and MQTT commands
3. [Development](development.md) - Contributing and extending

---

*This is a living MVP focused on core functionality. Features will grow from this solid base.*
