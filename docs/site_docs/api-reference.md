# API Reference

AmoraOS MVP 1 provides a Python SDK and MQTT interface for controlling music playback.

## Python SDK

### Basic Usage

```python
from amora_sdk import AmoraDevice

# Initialize device
device = AmoraDevice(device_id="amora-player-001")

# Connect to MQTT broker
await device.connect("localhost", 1883)

# Basic playback control
await device.play()
await device.pause()
await device.stop()
await device.next()
await device.previous()

# Volume control
await device.set_volume(75)  # 0-100

# Get status
status = await device.get_status()
print(f"Playing: {status.track_title}")
```

### SDK Methods

#### Playback Control

| Method | Description | Response Time |
|--------|-------------|---------------|
| `play()` | Start playback | ≤ 300ms |
| `pause()` | Pause playback | ≤ 300ms |
| `stop()` | Stop playback | ≤ 300ms |
| `next()` | Next track | ≤ 300ms |
| `previous()` | Previous track | ≤ 300ms |

#### Volume & Status

| Method | Parameters | Description |
|--------|------------|-------------|
| `set_volume(level)` | `level: int (0-100)` | Set volume level |
| `get_status()` | None | Get current player status |
| `get_playlists()` | None | List available playlists |
| `play_playlist(name)` | `name: str` | Play specific playlist |

#### Status Response

```python
{
    "state": "playing",  # playing, paused, stopped
    "track": {
        "title": "Song Title",
        "artist": "Artist Name", 
        "album": "Album Name",
        "duration": 240,  # seconds
        "position": 45    # seconds
    },
    "volume": 75,
    "playlist": "default"
}
```

## MQTT Interface

### Topic Structure

```
amora/devices/{device_id}/commands/{command}    # Commands to device
amora/devices/{device_id}/status                # Device status updates
amora/devices/{device_id}/heartbeat             # Device heartbeat (30s)
amora/devices/{device_id}/connection            # Connection status
```

### Command Messages

#### Play Command
```bash
mosquitto_pub -h localhost -t "amora/devices/player-001/commands/play" -m "{}"
```

#### Pause Command
```bash
mosquitto_pub -h localhost -t "amora/devices/player-001/commands/pause" -m "{}"
```

#### Volume Command
```bash
mosquitto_pub -h localhost -t "amora/devices/player-001/commands/setVolume" -m '{"level": 75}'
```

#### Status Request
```bash
mosquitto_pub -h localhost -t "amora/devices/player-001/commands/getStatus" -m "{}"
```

### Status Messages

Devices publish status updates automatically:

```json
{
    "timestamp": "2025-07-19T12:00:00Z",
    "device_id": "amora-player-001",
    "state": "playing",
    "track": {
        "title": "Example Song",
        "artist": "Example Artist",
        "duration": 240,
        "position": 45
    },
    "volume": 75,
    "playlist": "default"
}
```

### Heartbeat Messages

Every 30 seconds (±5s):

```json
{
    "timestamp": "2025-07-19T12:00:00Z",
    "device_id": "amora-player-001",
    "status": "online",
    "uptime": 3600,
    "cpu_temp": 45.2,
    "memory_usage": 65.4,
    "storage_free": 12.8
}
```

## Web UI Integration

The Web UI uses MQTT.js to communicate directly with devices:

```javascript
import mqtt from 'mqtt';

const client = mqtt.connect('ws://localhost:9001');

// Send play command
client.publish('amora/devices/player-001/commands/play', '{}');

// Listen for status updates
client.subscribe('amora/devices/+/status');
client.on('message', (topic, message) => {
    const status = JSON.parse(message.toString());
    updateUI(status);
});
```

## Error Handling

### Common Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| `DEVICE_OFFLINE` | Device not responding | Check network/power |
| `MQTT_DISCONNECTED` | MQTT broker unreachable | Verify broker status |
| `AUDIO_ERROR` | Audio system failure | Check audio configuration |
| `INVALID_COMMAND` | Malformed command | Validate JSON payload |

### Timeout Handling

- **Command timeout**: 5 seconds
- **Status timeout**: 2 seconds  
- **Connection timeout**: 10 seconds

## Performance Targets

| Operation | Target Latency |
|-----------|----------------|
| Play command → audio | ≤ 300ms |
| Status request → response | ≤ 200ms |
| Volume change → effect | ≤ 100ms |
| UI state sync | ≤ 200ms |
