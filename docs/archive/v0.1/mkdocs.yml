site_name: AmoraOS Documentation v0.1 (Archive)
site_description: Complete AmoraOS documentation archive - IoT Edge Audio Platform and SDK
site_author: AmoraOS Team

# Repository
repo_name: amora-os
repo_url: https://github.com/yourusername/amora-os

# Directories
docs_dir: docs
site_dir: ../../../site

# Version notice
extra:
  version:
    provider: mike
    default: v0.1
  versions:
    - version: v0.2
      title: v0.2 (MVP 1)
      aliases: [latest]
    - version: v0.1
      title: v0.1 (Archive)
      aliases: [archive]
  announcement: |
    📁 This is archived documentation (v0.1).
    <a href="../v0.2/">View current v0.2 documentation</a>

# Navigation
nav:
  - Home: index.md
  - User Guide:
    - Getting Started: getting_started.md
    - Configuration: configuration.md
  - Developer Guide:
    - Overview: developer_guide.md
    - Architecture: architecture.md
    - Data Flow: data_flow.md
  - IoT Integration:
    - Overview: iot_integration.md
    - Azure Architecture: azure_architecture.md
    - Azure Implementation: azure_implementation.md
    - Azure Resources: azure_resources.md
  - Real-time Communication: realtime_architecture.md
  - SDK:
    - Client Development: client_development.md
    - Device Integration: device_changes.md
    - Integration Guide: integration.md
  - Implementation:
    - Implementation Plan: implementation_plan.md

# Theme
theme:
  name: material
  palette:
    primary: orange  # Different color to indicate archive
    accent: orange
  features:
    - navigation.instant
    - navigation.tracking
    - navigation.expand
    - navigation.indexes
    - content.code.copy

# Extensions
markdown_extensions:
  - admonition
  - codehilite
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.details
  - toc:
      permalink: true

# Plugins
plugins:
  - search
