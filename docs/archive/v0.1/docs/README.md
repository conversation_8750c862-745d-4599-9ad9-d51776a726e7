# AmoraOS Documentation v0.1 Archive

This directory contains the archived version 0.1 of the AmoraOS documentation, saved on 2025-07-19.

## Contents

This archive includes:
- All documentation files from `docs/site_docs/`
- MkDocs configuration (`mkdocs.yml`)
- All images and assets

## Version 0.1 Features

The v0.1 documentation covered:
- Complete architecture overview with technical diagrams
- Azure IoT Hub integration details
- MQTT broker real-time communication
- SDK development guides
- Configuration and deployment instructions
- Getting started guides

## Navigation Structure

The v0.1 documentation had the following structure:
- Home: index.md
- User Guide:
  - Getting Started: getting_started.md
  - Configuration: configuration.md
- Developer Guide:
  - Overview: developer_guide.md
  - Architecture: architecture.md
  - Data Flow: data_flow.md
- IoT Integration:
  - Overview: iot_integration.md
  - Azure Architecture: azure_architecture.md
  - Azure Implementation: azure_implementation.md
  - Azure Resources: azure_resources.md
- Real-time Communication: realtime_architecture.md
- SDK:
  - Client Development: client_development.md
  - Device Integration: device_changes.md
  - Integration Guide: integration.md
- Implementation:
  - Implementation Plan: implementation_plan.md

## Images

- amora-os-overview.jpeg - High-level architecture overview
- amora-os-overview-v0.1.png - Detailed technical architecture
- realtime_architecture.png - Real-time communication diagram

## Notes

This version represents the comprehensive documentation state before transitioning to v0.2 with a fresh, minimal approach based on the new MVP strategy.
