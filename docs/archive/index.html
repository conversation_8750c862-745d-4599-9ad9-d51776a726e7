<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AmoraOS Documentation Archive</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        .version-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            background: #f9f9f9;
        }
        .version-card h3 {
            margin-top: 0;
            color: #333;
        }
        .version-card a {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        .version-card a:hover {
            background: #005a9e;
        }
        .current {
            border-color: #007acc;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>AmoraOS Documentation Versions</h1>
    
    <div class="version-card current">
        <h3>v0.2 - MVP 1 (Current)</h3>
        <p>Minimal documentation focused on core MVP functionality:</p>
        <ul>
            <li>Raspberry Pi music box with MQTT control</li>
            <li>Local Web UI</li>
            <li>Quick start guide</li>
            <li>API reference</li>
        </ul>
        <a href="../">View v0.2 Documentation</a>
    </div>

    <div class="version-card">
        <h3>v0.1 - Complete Architecture (Archive)</h3>
        <p>Comprehensive documentation with full architecture details:</p>
        <ul>
            <li>Complete Azure IoT Hub integration</li>
            <li>Detailed architecture diagrams</li>
            <li>Full SDK documentation</li>
            <li>Implementation plans</li>
        </ul>
        <a href="v0.1/">View v0.1 Archive</a>
    </div>

    <hr>
    <p><small>
        <a href="../">← Back to Current Documentation</a> | 
        <a href="https://github.com/yourusername/amora-os">GitHub Repository</a>
    </small></p>
</body>
</html>
